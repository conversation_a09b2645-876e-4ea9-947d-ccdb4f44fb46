// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 90;
	objects = {

/* Begin PBXBuildFile section */
		6B85BF2719F74EA400B7F16A /* FXBlurView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6B85BF2619F74EA400B7F16A /* FXBlurView.m */; };
		6B85BF2919F74EB400B7F16A /* Accelerate.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6B85BF2819F74EB400B7F16A /* Accelerate.framework */; };
		6B85BF2B19F74EB800B7F16A /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6B85BF2A19F74EB800B7F16A /* QuartzCore.framework */; };
		96CFD13F168D308D00BAEEE6 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 96CFD13E168D308D00BAEEE6 /* UIKit.framework */; };
		96CFD141168D308D00BAEEE6 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 96CFD140168D308D00BAEEE6 /* Foundation.framework */; };
		96CFD143168D308D00BAEEE6 /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 96CFD142168D308D00BAEEE6 /* CoreGraphics.framework */; };
		96CFD149168D308D00BAEEE6 /* InfoPlist.strings in Resources */ = {isa = PBXBuildFile; fileRef = 96CFD147168D308D00BAEEE6 /* InfoPlist.strings */; };
		96CFD14B168D308D00BAEEE6 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 96CFD14A168D308D00BAEEE6 /* main.m */; };
		96CFD14F168D308D00BAEEE6 /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 96CFD14E168D308D00BAEEE6 /* AppDelegate.m */; };
		96CFD151168D308D00BAEEE6 /* Default.png in Resources */ = {isa = PBXBuildFile; fileRef = 96CFD150168D308D00BAEEE6 /* Default.png */; };
		96CFD153168D308D00BAEEE6 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96CFD152168D308D00BAEEE6 /* <EMAIL> */; };
		96CFD155168D308D00BAEEE6 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96CFD154168D308D00BAEEE6 /* <EMAIL> */; };
		96CFD158168D308D00BAEEE6 /* ViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96CFD157168D308D00BAEEE6 /* ViewController.m */; };
		96CFD163168D309800BAEEE6 /* ImageCropView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96CFD162168D309800BAEEE6 /* ImageCropView.m */; };
		CE786DB61910477500800637 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = CE786DB51910477500800637 /* Main.storyboard */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		6B85BF2519F74EA400B7F16A /* FXBlurView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FXBlurView.h; sourceTree = "<group>"; };
		6B85BF2619F74EA400B7F16A /* FXBlurView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FXBlurView.m; sourceTree = "<group>"; };
		6B85BF2819F74EB400B7F16A /* Accelerate.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Accelerate.framework; path = System/Library/Frameworks/Accelerate.framework; sourceTree = SDKROOT; };
		6B85BF2A19F74EB800B7F16A /* QuartzCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuartzCore.framework; path = System/Library/Frameworks/QuartzCore.framework; sourceTree = SDKROOT; };
		96CFD13A168D308D00BAEEE6 /* ImageCropView.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = ImageCropView.app; sourceTree = BUILT_PRODUCTS_DIR; };
		96CFD13E168D308D00BAEEE6 /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = System/Library/Frameworks/UIKit.framework; sourceTree = SDKROOT; };
		96CFD140168D308D00BAEEE6 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = System/Library/Frameworks/Foundation.framework; sourceTree = SDKROOT; };
		96CFD142168D308D00BAEEE6 /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = System/Library/Frameworks/CoreGraphics.framework; sourceTree = SDKROOT; };
		96CFD146168D308D00BAEEE6 /* ImageCropView-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "ImageCropView-Info.plist"; sourceTree = "<group>"; };
		96CFD148168D308D00BAEEE6 /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		96CFD14A168D308D00BAEEE6 /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		96CFD14C168D308D00BAEEE6 /* ImageCropView-Prefix.pch */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "ImageCropView-Prefix.pch"; sourceTree = "<group>"; };
		96CFD14D168D308D00BAEEE6 /* AppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		96CFD14E168D308D00BAEEE6 /* AppDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		96CFD150168D308D00BAEEE6 /* Default.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = Default.png; sourceTree = "<group>"; };
		96CFD152168D308D00BAEEE6 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96CFD154168D308D00BAEEE6 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96CFD156168D308D00BAEEE6 /* ViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ViewController.h; sourceTree = "<group>"; };
		96CFD157168D308D00BAEEE6 /* ViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ViewController.m; sourceTree = "<group>"; };
		96CFD161168D309800BAEEE6 /* ImageCropView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ImageCropView.h; sourceTree = "<group>"; };
		96CFD162168D309800BAEEE6 /* ImageCropView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ImageCropView.m; sourceTree = "<group>"; };
		CE786DB51910477500800637 /* Main.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; path = Main.storyboard; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		96CFD137168D308D00BAEEE6 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			files = (
				6B85BF2B19F74EB800B7F16A /* QuartzCore.framework in Frameworks */,
				6B85BF2919F74EB400B7F16A /* Accelerate.framework in Frameworks */,
				96CFD13F168D308D00BAEEE6 /* UIKit.framework in Frameworks */,
				96CFD141168D308D00BAEEE6 /* Foundation.framework in Frameworks */,
				96CFD143168D308D00BAEEE6 /* CoreGraphics.framework in Frameworks */,
			);
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		6B85BF2419F74EA400B7F16A /* FXBlurView */ = {
			isa = PBXGroup;
			children = (
				6B85BF2519F74EA400B7F16A /* FXBlurView.h */,
				6B85BF2619F74EA400B7F16A /* FXBlurView.m */,
			);
			path = FXBlurView;
			sourceTree = "<group>";
		};
		96CFD12F168D308D00BAEEE6 = {
			isa = PBXGroup;
			children = (
				96CFD166168D458A00BAEEE6 /* Demo */,
				96CFD144168D308D00BAEEE6 /* ImageCropView */,
				96CFD13D168D308D00BAEEE6 /* Frameworks */,
				96CFD13B168D308D00BAEEE6 /* Products */,
			);
			sourceTree = "<group>";
		};
		96CFD13B168D308D00BAEEE6 /* Products */ = {
			isa = PBXGroup;
			children = (
				96CFD13A168D308D00BAEEE6 /* ImageCropView.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		96CFD13D168D308D00BAEEE6 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				6B85BF2A19F74EB800B7F16A /* QuartzCore.framework */,
				6B85BF2819F74EB400B7F16A /* Accelerate.framework */,
				96CFD13E168D308D00BAEEE6 /* UIKit.framework */,
				96CFD140168D308D00BAEEE6 /* Foundation.framework */,
				96CFD142168D308D00BAEEE6 /* CoreGraphics.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		96CFD144168D308D00BAEEE6 /* ImageCropView */ = {
			isa = PBXGroup;
			children = (
				96CFD162168D309800BAEEE6 /* ImageCropView.m */,
				96CFD161168D309800BAEEE6 /* ImageCropView.h */,
			);
			path = ImageCropView;
			sourceTree = "<group>";
		};
		96CFD145168D308D00BAEEE6 /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				96CFD146168D308D00BAEEE6 /* ImageCropView-Info.plist */,
				96CFD147168D308D00BAEEE6 /* InfoPlist.strings */,
				96CFD14A168D308D00BAEEE6 /* main.m */,
				96CFD14C168D308D00BAEEE6 /* ImageCropView-Prefix.pch */,
				96CFD150168D308D00BAEEE6 /* Default.png */,
				96CFD152168D308D00BAEEE6 /* <EMAIL> */,
				96CFD154168D308D00BAEEE6 /* <EMAIL> */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		96CFD166168D458A00BAEEE6 /* Demo */ = {
			isa = PBXGroup;
			children = (
				6B85BF2419F74EA400B7F16A /* FXBlurView */,
				96CFD145168D308D00BAEEE6 /* Supporting Files */,
				96CFD14D168D308D00BAEEE6 /* AppDelegate.h */,
				96CFD14E168D308D00BAEEE6 /* AppDelegate.m */,
				CE786DB51910477500800637 /* Main.storyboard */,
				96CFD156168D308D00BAEEE6 /* ViewController.h */,
				96CFD157168D308D00BAEEE6 /* ViewController.m */,
			);
			name = Demo;
			path = ImageCropView;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		96CFD139168D308D00BAEEE6 /* ImageCropView */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 96CFD15E168D308D00BAEEE6 /* Build configuration list for PBXNativeTarget "ImageCropView" */;
			buildPhases = (
				96CFD136168D308D00BAEEE6 /* Sources */,
				96CFD137168D308D00BAEEE6 /* Frameworks */,
				96CFD138168D308D00BAEEE6 /* Resources */,
			);
			buildRules = (
			);
			name = ImageCropView;
			productName = ImageCropView;
			productReference = 96CFD13A168D308D00BAEEE6 /* ImageCropView.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		96CFD131168D308D00BAEEE6 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0450;
			};
			buildConfigurationList = 96CFD134168D308D00BAEEE6 /* Build configuration list for PBXProject "ImageCropView" */;
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				English,
				en,
			);
			mainGroup = 96CFD12F168D308D00BAEEE6;
			preferredProjectObjectVersion = 90;
			productRefGroup = 96CFD13B168D308D00BAEEE6 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				96CFD139168D308D00BAEEE6 /* ImageCropView */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		96CFD138168D308D00BAEEE6 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			files = (
				96CFD149168D308D00BAEEE6 /* InfoPlist.strings in Resources */,
				CE786DB61910477500800637 /* Main.storyboard in Resources */,
				96CFD151168D308D00BAEEE6 /* Default.png in Resources */,
				96CFD153168D308D00BAEEE6 /* <EMAIL> in Resources */,
				96CFD155168D308D00BAEEE6 /* <EMAIL> in Resources */,
			);
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		96CFD136168D308D00BAEEE6 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			files = (
				96CFD14B168D308D00BAEEE6 /* main.m in Sources */,
				96CFD14F168D308D00BAEEE6 /* AppDelegate.m in Sources */,
				6B85BF2719F74EA400B7F16A /* FXBlurView.m in Sources */,
				96CFD158168D308D00BAEEE6 /* ViewController.m in Sources */,
				96CFD163168D309800BAEEE6 /* ImageCropView.m in Sources */,
			);
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		96CFD147168D308D00BAEEE6 /* InfoPlist.strings */ = {
			isa = PBXVariantGroup;
			children = (
				96CFD148168D308D00BAEEE6 /* en */,
			);
			name = InfoPlist.strings;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		96CFD15C168D308D00BAEEE6 /* Debug configuration for PBXProject "ImageCropView" */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 6.0;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		96CFD15D168D308D00BAEEE6 /* Release configuration for PBXProject "ImageCropView" */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 6.0;
				OTHER_CFLAGS = "-DNS_BLOCK_ASSERTIONS=1";
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		96CFD15F168D308D00BAEEE6 /* Debug configuration for PBXNativeTarget "ImageCropView" */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ENABLE_OBJC_ARC = YES;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "ImageCropView/ImageCropView-Prefix.pch";
				INFOPLIST_FILE = "ImageCropView/ImageCropView-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				PRODUCT_NAME = "$(TARGET_NAME)";
				WRAPPER_EXTENSION = app;
			};
			name = Debug;
		};
		96CFD160168D308D00BAEEE6 /* Release configuration for PBXNativeTarget "ImageCropView" */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ENABLE_OBJC_ARC = YES;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "ImageCropView/ImageCropView-Prefix.pch";
				INFOPLIST_FILE = "ImageCropView/ImageCropView-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				PRODUCT_NAME = "$(TARGET_NAME)";
				WRAPPER_EXTENSION = app;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		96CFD134168D308D00BAEEE6 /* Build configuration list for PBXProject "ImageCropView" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				96CFD15C168D308D00BAEEE6 /* Debug configuration for PBXProject "ImageCropView" */,
				96CFD15D168D308D00BAEEE6 /* Release configuration for PBXProject "ImageCropView" */,
			);
			defaultConfigurationName = Release;
		};
		96CFD15E168D308D00BAEEE6 /* Build configuration list for PBXNativeTarget "ImageCropView" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				96CFD15F168D308D00BAEEE6 /* Debug configuration for PBXNativeTarget "ImageCropView" */,
				96CFD160168D308D00BAEEE6 /* Release configuration for PBXNativeTarget "ImageCropView" */,
			);
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 96CFD131168D308D00BAEEE6 /* Project object */;
}
